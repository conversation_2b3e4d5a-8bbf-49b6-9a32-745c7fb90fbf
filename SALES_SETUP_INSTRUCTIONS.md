# Sales Management System Setup Instructions

## Overview
The sales management system has been created to handle sales transactions with automatic stock quantity updates. When a product is sold, the system automatically deducts the quantity from the product inventory and updates the product status accordingly.

## Database Setup

### 1. Create Sales Tables
Run the SQL script to create the required tables:

```bash
mysql -u your_username -p your_database_name < database/create_sales_tables.sql
```

Or manually execute the SQL commands in `database/create_sales_tables.sql` in your MySQL database.

### 2. Required Tables
The system creates the following tables:
- `sales` - Main sales records
- `sale_items` - Individual items in each sale
- Views: `sales_summary` and `sales_with_items` for reporting

## Features Implemented

### 1. Sales Creation
- **File**: `sales.php`
- **Features**:
  - Create new sales with multiple products
  - Select customers from existing customer list
  - Automatic stock quantity validation
  - Real-time price calculation
  - Multiple payment methods support
  - Automatic stock deduction upon sale completion
  - Product status updates (In Stock → Low Stock → Out of Stock)

### 2. Stock Management
- **Automatic Stock Updates**: When a sale is completed, the system:
  - Deducts sold quantities from product stock
  - Updates product status based on remaining stock:
    - Stock = 0: "Out of Stock"
    - Stock ≤ 10: "Low Stock" 
    - Stock > 10: "In Stock"

### 3. Sales Dashboard
- **Real-time Statistics**:
  - Today's sales total and count
  - Monthly sales summary
  - Average order value
  - Active customers count

### 4. Sales History
- View all sales with customer details
- Sales items count and total amounts
- Payment status tracking
- Action buttons for viewing and printing

## Database Schema

### Sales Table
```sql
- id (Primary Key)
- customer_id (Foreign Key to users table)
- sale_date
- total_amount
- payment_method (Cash, Credit Card, etc.)
- payment_status (Paid, Pending, Partial, Refunded)
- notes
- created_by (Foreign Key to users table)
- created_at, updated_at
```

### Sale Items Table
```sql
- id (Primary Key)
- sale_id (Foreign Key to sales table)
- product_id (Foreign Key to products table)
- quantity
- unit_price
- subtotal
- created_at
```

## Usage Instructions

### Creating a New Sale
1. Navigate to `sales.php`
2. Click "New Sale" button
3. Select customer from dropdown
4. Choose sale date and payment method
5. Add products by clicking "Add Item"
6. For each item:
   - Select product (shows current price and stock)
   - Enter quantity (validates against available stock)
   - Adjust unit price if needed
   - View automatic subtotal calculation
7. Add notes if needed
8. Review total amount
9. Click "Create Sale"

### Stock Validation
- System prevents selling more than available stock
- Shows real-time stock levels in product dropdown
- Validates quantities before processing sale
- Updates product status automatically

### Transaction Safety
- Uses database transactions for data integrity
- Rollback on any errors during sale processing
- Prevents partial updates if any step fails

## Files Created/Modified

### New Files:
- `database/create_sales_tables.sql` - Database schema
- `get_sale_details.php` - API for sale details
- `SALES_SETUP_INSTRUCTIONS.md` - This file

### Modified Files:
- `sales.php` - Complete sales management interface

## Security Features
- Session-based authentication required
- SQL injection prevention with prepared statements
- Input validation and sanitization
- Transaction-based data integrity
- Error logging without exposing sensitive data

## Future Enhancements
The system is designed to be extensible. Potential additions:
- Receipt printing functionality
- Sales reporting and analytics
- Inventory alerts for low stock
- Customer purchase history
- Barcode scanning support
- Multi-location inventory management

## Testing
1. Ensure you have products in your `products` table
2. Ensure you have customers in your `users` table with user_type = 'Customer'
3. Test creating a sale with various scenarios:
   - Single item sale
   - Multiple items sale
   - Quantity validation (try to sell more than available)
   - Different payment methods

## Support
If you encounter any issues:
1. Check database connections in `database/conn.php`
2. Verify all required tables exist
3. Check PHP error logs for detailed error messages
4. Ensure proper user permissions for database operations
