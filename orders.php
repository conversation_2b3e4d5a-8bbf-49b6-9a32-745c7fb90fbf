<?php
require_once 'database/conn.php';
require_once 'check_session.php';

// Require user to be logged in
requireLogin();

// Initialize variables
$message = '';
$messageType = '';
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_range = $_GET['date_range'] ?? '';
$payment_status = $_GET['payment_status'] ?? '';
$customer_filter = $_GET['customer'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_order':
                // Handle create new order
                $customer_id = filter_var($_POST['customer_id'] ?? 0, FILTER_VALIDATE_INT);
                $order_date = trim($_POST['order_date'] ?? date('Y-m-d'));
                $payment_method = trim($_POST['payment_method'] ?? '');
                $status = trim($_POST['status'] ?? 'pending');
                $notes = trim($_POST['notes'] ?? '');
                $order_items = json_decode($_POST['order_items'] ?? '[]', true);

                // Server-side validation
                $errors = [];
                if ($customer_id === false || $customer_id <= 0) $errors[] = 'Valid customer is required';
                if (empty($order_items) || !is_array($order_items)) $errors[] = 'At least one product is required';
                if (empty($payment_method)) $errors[] = 'Payment method is required';

                if (!empty($errors)) {
                    $message = implode(', ', $errors);
                    $messageType = 'error';
                } else {
                    try {
                        $pdo->beginTransaction();

                        // Generate unique order number
                        $order_number = 'ORD-' . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);

                        // Check if order number already exists
                        $check_stmt = $pdo->prepare("SELECT id FROM orders WHERE order_number = ?");
                        $check_stmt->execute([$order_number]);
                        while ($check_stmt->rowCount() > 0) {
                            $order_number = 'ORD-' . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
                            $check_stmt->execute([$order_number]);
                        }

                        // Calculate total amount
                        $total_amount = 0;
                        foreach ($order_items as $item) {
                            $total_amount += $item['price'] * $item['quantity'];
                        }

                        // Insert new order
                        $insert_order_stmt = $pdo->prepare("
                            INSERT INTO orders (order_number, customer_id, total_amount, status, order_date, notes, created_at, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
                        ");

                        $result = $insert_order_stmt->execute([
                            $order_number,
                            $customer_id,
                            $total_amount,
                            $status,
                            $order_date,
                            $notes
                        ]);

                        if ($result) {
                            $order_id = $pdo->lastInsertId();

                            // Insert order items
                            $insert_item_stmt = $pdo->prepare("
                                INSERT INTO order_items (order_id, product_id, quantity, price, subtotal, created_at)
                                VALUES (?, ?, ?, ?, ?, NOW())
                            ");

                            foreach ($order_items as $item) {
                                $subtotal = $item['price'] * $item['quantity'];
                                $insert_item_stmt->execute([
                                    $order_id,
                                    $item['product_id'],
                                    $item['quantity'],
                                    $item['price'],
                                    $subtotal
                                ]);
                            }

                            $pdo->commit();
                            $message = 'Order created successfully!';
                            $messageType = 'success';
                        } else {
                            $pdo->rollBack();
                            $message = 'Failed to create order. Please try again.';
                            $messageType = 'error';
                        }
                    } catch (PDOException $e) {
                        $pdo->rollBack();
                        error_log("Order creation error: " . $e->getMessage());
                        $message = 'Database error occurred. Please try again.';
                        $messageType = 'error';
                    }
                }
                break;

            case 'update_order_status':
                // Handle order status update
                $order_id = filter_var($_POST['order_id'] ?? 0, FILTER_VALIDATE_INT);
                $new_status = trim($_POST['new_status'] ?? '');

                if ($order_id === false || $order_id <= 0) {
                    $message = 'Invalid order ID.';
                    $messageType = 'error';
                } elseif (empty($new_status)) {
                    $message = 'Status is required.';
                    $messageType = 'error';
                } else {
                    try {
                        $update_stmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?");
                        $result = $update_stmt->execute([$new_status, $order_id]);

                        if ($result) {
                            $message = 'Order status updated successfully!';
                            $messageType = 'success';
                        } else {
                            $message = 'Failed to update order status.';
                            $messageType = 'error';
                        }
                    } catch (PDOException $e) {
                        error_log("Order status update error: " . $e->getMessage());
                        $message = 'Database error occurred. Please try again.';
                        $messageType = 'error';
                    }
                }
                break;

            case 'delete_order':
                // Handle delete order
                $order_id = filter_var($_POST['order_id'] ?? 0, FILTER_VALIDATE_INT);

                if ($order_id === false || $order_id <= 0) {
                    $message = 'Invalid order ID.';
                    $messageType = 'error';
                } else {
                    try {
                        $pdo->beginTransaction();

                        // Delete order items first (due to foreign key constraint)
                        $delete_items_stmt = $pdo->prepare("DELETE FROM order_items WHERE order_id = ?");
                        $delete_items_stmt->execute([$order_id]);

                        // Delete the order
                        $delete_order_stmt = $pdo->prepare("DELETE FROM orders WHERE id = ?");
                        $result = $delete_order_stmt->execute([$order_id]);

                        if ($result) {
                            $pdo->commit();
                            $message = 'Order deleted successfully!';
                            $messageType = 'success';
                        } else {
                            $pdo->rollBack();
                            $message = 'Failed to delete order.';
                            $messageType = 'error';
                        }
                    } catch (PDOException $e) {
                        $pdo->rollBack();
                        error_log("Order delete error: " . $e->getMessage());
                        $message = 'Database error occurred. Please try again.';
                        $messageType = 'error';
                    }
                }
                break;
        }
    }
}

// Build WHERE clause for filtering
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(o.order_number LIKE ? OR u.name LIKE ? OR u.email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "o.status = ?";
    $params[] = $status_filter;
}

if (!empty($customer_filter)) {
    $where_conditions[] = "o.customer_id = ?";
    $params[] = $customer_filter;
}

// Handle date range filtering
if (!empty($date_range)) {
    switch ($date_range) {
        case 'today':
            $where_conditions[] = "DATE(o.order_date) = CURDATE()";
            break;
        case 'yesterday':
            $where_conditions[] = "DATE(o.order_date) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
            break;
        case 'last_7_days':
            $where_conditions[] = "DATE(o.order_date) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)";
            break;
        case 'last_30_days':
            $where_conditions[] = "DATE(o.order_date) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
            break;
        case 'this_month':
            $where_conditions[] = "MONTH(o.order_date) = MONTH(CURDATE()) AND YEAR(o.order_date) = YEAR(CURDATE())";
            break;
    }
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get order statistics
try {
    $stats_query = "
        SELECT
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
            SUM(total_amount) as total_revenue
        FROM orders o
    ";
    $stats_result = $pdo->query($stats_query);
    $stats = $stats_result->fetch();
} catch (PDOException $e) {
    $stats = [
        'total_orders' => 0,
        'pending_orders' => 0,
        'completed_orders' => 0,
        'cancelled_orders' => 0,
        'total_revenue' => 0
    ];
}

// Get customers for dropdown
try {
    $customers_query = "SELECT id, name, email FROM users WHERE user_type = 'Customer' ORDER BY name";
    $customers_result = $pdo->query($customers_query);
    $customers = $customers_result->fetchAll();
} catch (PDOException $e) {
    $customers = [];
}

// Get products for dropdown
try {
    $products_query = "SELECT id, product, price, stock FROM products WHERE status = 'Available' ORDER BY product";
    $products_result = $pdo->query($products_query);
    $products = $products_result->fetchAll();
} catch (PDOException $e) {
    $products = [];
}

// Get total count for pagination
try {
    $count_query = "
        SELECT COUNT(*) as total
        FROM orders o
        LEFT JOIN users u ON o.customer_id = u.id
        $where_clause
    ";
    $count_stmt = $pdo->prepare($count_query);
    $count_stmt->execute($params);
    $total_records = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_records / $limit);
} catch (PDOException $e) {
    $total_records = 0;
    $total_pages = 1;
}

// Get orders data
try {
    $orders_query = "
        SELECT
            o.id,
            o.order_number,
            o.total_amount,
            o.status,
            o.order_date,
            o.notes,
            u.name as customer_name,
            u.email as customer_email,
            COUNT(oi.id) as item_count,
            GROUP_CONCAT(CONCAT(p.product, ' (', oi.quantity, ')') SEPARATOR ', ') as products_summary
        FROM orders o
        LEFT JOIN users u ON o.customer_id = u.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        $where_clause
        GROUP BY o.id
        ORDER BY o.order_date DESC
        LIMIT $limit OFFSET $offset
    ";
    $orders_stmt = $pdo->prepare($orders_query);
    $orders_stmt->execute($params);
    $orders = $orders_stmt->fetchAll();
} catch (PDOException $e) {
    $orders = [];
    $message = "Error loading orders data.";
    $messageType = 'error';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orders - Meat Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-100" x-data="{
    sidebarOpen: false,
    showCreateOrderModal: false,
    orderItems: [],
    selectedCustomer: '',
    addProductToOrder(productId, productName, price, quantity = 1) {
        const existingItem = this.orderItems.find(item => item.productId === productId);
        if (existingItem) {
            existingItem.quantity += parseInt(quantity);
        } else {
            this.orderItems.push({
                productId: productId,
                productName: productName,
                price: parseFloat(price),
                quantity: parseInt(quantity)
            });
        }
    },
    removeOrderItem(index) {
        this.orderItems.splice(index, 1);
    },
    calculateTotal() {
        return this.orderItems.reduce((total, item) => total + (item.price * item.quantity), 0).toFixed(2);
    },
    submitOrder() {
        if (this.orderItems.length === 0) {
            alert('Please add at least one product to the order.');
            return;
        }
        if (!this.selectedCustomer) {
            alert('Please select a customer.');
            return;
        }

        const orderData = {
            action: 'create_order',
            customer_id: this.selectedCustomer,
            order_date: document.getElementById('order-date').value || new Date().toISOString().split('T')[0],
            payment_method: document.getElementById('payment-method').value,
            status: document.getElementById('order-status').value,
            notes: document.getElementById('notes').value,
            order_items: JSON.stringify(this.orderItems.map(item => ({
                product_id: item.productId,
                quantity: item.quantity,
                price: item.price
            })))
        };

        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';

        Object.keys(orderData).forEach(key => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = orderData[key];
            form.appendChild(input);
        });

        document.body.appendChild(form);
        form.submit();
    }
}">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include("include/sidebar.php") ?>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <?php include("include/top_navbar.php") ?>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Message Display -->
                <?php if (!empty($message)): ?>
                    <div class="mb-4 p-4 rounded-lg <?= $messageType === 'error' ? 'bg-red-100 border border-red-400 text-red-700' : 'bg-green-100 border border-green-400 text-green-700' ?>">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas <?= $messageType === 'error' ? 'fa-exclamation-circle' : 'fa-check-circle' ?> h-5 w-5"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium"><?= htmlspecialchars($message) ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Page Header -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Order Management</h1>
                        <p class="mt-1 text-sm text-gray-600">View and manage customer orders</p>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <button @click="showCreateOrderModal = true" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-plus mr-2"></i> Create Order
                        </button>
                    </div>
                </div>

                <!-- Order Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Total Orders</p>
                                <h3 class="text-xl font-semibold"><?= number_format($stats['total_orders'] ?? 0) ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Pending</p>
                                <h3 class="text-xl font-semibold"><?= number_format($stats['pending_orders'] ?? 0) ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Completed</p>
                                <h3 class="text-xl font-semibold"><?= number_format($stats['completed_orders'] ?? 0) ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-red-100 text-red-600">
                                <i class="fas fa-times"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Cancelled</p>
                                <h3 class="text-xl font-semibold"><?= number_format($stats['cancelled_orders'] ?? 0) ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow p-4 mb-6">
                    <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <?php if (!empty($search)): ?>
                            <input type="hidden" name="search" value="<?= htmlspecialchars($search) ?>">
                        <?php endif; ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Order Status</label>
                            <select name="status" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">All Status</option>
                                <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>Pending</option>
                                <option value="processing" <?= $status_filter === 'processing' ? 'selected' : '' ?>>Processing</option>
                                <option value="completed" <?= $status_filter === 'completed' ? 'selected' : '' ?>>Completed</option>
                                <option value="cancelled" <?= $status_filter === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                                <option value="refunded" <?= $status_filter === 'refunded' ? 'selected' : '' ?>>Refunded</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                            <select name="date_range" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">All Dates</option>
                                <option value="today" <?= $date_range === 'today' ? 'selected' : '' ?>>Today</option>
                                <option value="yesterday" <?= $date_range === 'yesterday' ? 'selected' : '' ?>>Yesterday</option>
                                <option value="last_7_days" <?= $date_range === 'last_7_days' ? 'selected' : '' ?>>Last 7 Days</option>
                                <option value="last_30_days" <?= $date_range === 'last_30_days' ? 'selected' : '' ?>>Last 30 Days</option>
                                <option value="this_month" <?= $date_range === 'this_month' ? 'selected' : '' ?>>This Month</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                            <select name="payment_status" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">All Payments</option>
                                <option value="paid" <?= $payment_status === 'paid' ? 'selected' : '' ?>>Paid</option>
                                <option value="unpaid" <?= $payment_status === 'unpaid' ? 'selected' : '' ?>>Unpaid</option>
                                <option value="refunded" <?= $payment_status === 'refunded' ? 'selected' : '' ?>>Refunded</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Customer</label>
                            <select name="customer" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">All Customers</option>
                                <?php foreach ($customers as $customer): ?>
                                    <option value="<?= $customer['id'] ?>" <?= $customer_filter == $customer['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($customer['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="flex items-end space-x-2">
                            <button type="submit" class="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Apply Filters
                            </button>
                            <a href="orders.php" class="bg-gray-300 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500" title="Clear Filters">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </form>
                </div>

                <!-- Orders Table -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Recent Orders</h3>
                        <div class="flex items-center space-x-2">
                            <button class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-file-export"></i>
                            </button>
                            <button class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Products</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (empty($orders)): ?>
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            <div class="flex flex-col items-center">
                                                <i class="fas fa-shopping-cart text-4xl text-gray-300 mb-2"></i>
                                                <p>No orders found.</p>
                                                <?php if (!empty($search) || !empty($status_filter) || !empty($date_range) || !empty($customer_filter)): ?>
                                                    <p class="text-sm mt-1">Try adjusting your filters.</p>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($orders as $order): ?>
                                        <?php
                                        // Determine status styling
                                        $status_classes = [
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'processing' => 'bg-blue-100 text-blue-800',
                                            'completed' => 'bg-green-100 text-green-800',
                                            'cancelled' => 'bg-red-100 text-red-800',
                                            'refunded' => 'bg-gray-100 text-gray-800'
                                        ];
                                        $status_class = $status_classes[$order['status']] ?? 'bg-gray-100 text-gray-800';

                                        // Truncate products summary if too long
                                        $products_display = $order['products_summary'] ?? 'No products';
                                        if (strlen($products_display) > 50) {
                                            $products_display = substr($products_display, 0, 47) . '...';
                                        }
                                        ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($order['order_number']) ?></div>
                                                <div class="text-sm text-gray-500"><?= date('M j, Y', strtotime($order['order_date'])) ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-10 w-10">
                                                        <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                            <i class="fas fa-user text-gray-600"></i>
                                                        </div>
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($order['customer_name'] ?? 'N/A') ?></div>
                                                        <div class="text-sm text-gray-500"><?= htmlspecialchars($order['customer_email'] ?? 'N/A') ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-gray-900"><?= htmlspecialchars($products_display) ?></div>
                                                <?php if ($order['item_count'] > 1): ?>
                                                    <div class="text-sm text-gray-500"><?= $order['item_count'] ?> items total</div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">$<?= number_format($order['total_amount'], 2) ?></div>
                                                <div class="text-sm text-gray-500">
                                                    <?php
                                                    // Check payment status (simplified - you might want to join with payments table)
                                                    echo $order['status'] === 'completed' ? 'Paid' : 'Pending';
                                                    ?>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $status_class ?>">
                                                    <?= ucfirst($order['status']) ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="#" onclick="viewOrderDetails(<?= $order['id'] ?>)" class="text-indigo-600 hover:text-indigo-900 mr-3" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="#" onclick="editOrderStatus(<?= $order['id'] ?>, '<?= htmlspecialchars($order['status']) ?>')" class="text-green-600 hover:text-green-900 mr-3" title="Edit Status">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="#" onclick="deleteOrder(<?= $order['id'] ?>, '<?= htmlspecialchars($order['order_number']) ?>')" class="text-red-600 hover:text-red-900" title="Delete Order">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- Pagination -->
                    <?php if ($total_records > 0): ?>
                        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <?php if ($page > 1): ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</a>
                                <?php endif; ?>
                                <?php if ($page < $total_pages): ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</a>
                                <?php endif; ?>
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        Showing <span class="font-medium"><?= ($page - 1) * $limit + 1 ?></span> to <span class="font-medium"><?= min($page * $limit, $total_records) ?></span> of <span class="font-medium"><?= $total_records ?></span> results
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <?php if ($page > 1): ?>
                                            <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <span class="sr-only">Previous</span>
                                                <i class="fas fa-chevron-left h-5 w-5"></i>
                                            </a>
                                        <?php endif; ?>

                                        <?php
                                        $start_page = max(1, $page - 2);
                                        $end_page = min($total_pages, $page + 2);

                                        for ($i = $start_page; $i <= $end_page; $i++):
                                        ?>
                                            <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"
                                               class="<?= $i === $page ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50' ?> relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                                <?= $i ?>
                                            </a>
                                        <?php endfor; ?>

                                        <?php if ($page < $total_pages): ?>
                                            <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <span class="sr-only">Next</span>
                                                <i class="fas fa-chevron-right h-5 w-5"></i>
                                            </a>
                                        <?php endif; ?>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>

    <!-- Create Order Modal -->
    <div x-show="showCreateOrderModal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true" x-cloak>
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showCreateOrderModal"
                 x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                 @click="showCreateOrderModal = false"
                 aria-hidden="true"></div>

            <!-- Modal Panel -->
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div x-show="showCreateOrderModal"
                 x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">

                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <div class="flex justify-between items-center">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                    Create New Order
                                </h3>
                                <button @click="showCreateOrderModal = false" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>

                            <form method="POST" class="mt-5 space-y-6" @submit.prevent="submitOrder()">
                                <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                    <!-- Customer Selection -->
                                    <div class="sm:col-span-3">
                                        <label for="customer" class="block text-sm font-medium text-gray-700">Customer</label>
                                        <select id="customer" name="customer_id" x-model="selectedCustomer" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                            <option value="">Select a customer</option>
                                            <?php foreach ($customers as $customer): ?>
                                                <option value="<?= $customer['id'] ?>"><?= htmlspecialchars($customer['name']) ?> (<?= htmlspecialchars($customer['email']) ?>)</option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <!-- Order Date -->
                                    <div class="sm:col-span-3">
                                        <label for="order-date" class="block text-sm font-medium text-gray-700">Order Date</label>
                                        <div class="mt-1">
                                            <input type="date" name="order-date" id="order-date" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md p-2">
                                        </div>
                                    </div>

                                    <!-- Product Selection -->
                                    <div class="sm:col-span-6">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Add Products</label>
                                        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4" x-data="{
                                                selectedProductId: '',
                                                selectedProductName: '',
                                                selectedProductPrice: 0,
                                                quantity: 1,
                                                updateSelectedProduct() {
                                                    const select = document.getElementById('product-select');
                                                    const option = select.options[select.selectedIndex];
                                                    this.selectedProductId = select.value;
                                                    this.selectedProductName = option.getAttribute('data-name');
                                                    this.selectedProductPrice = parseFloat(option.getAttribute('data-price'));
                                                }
                                            }">
                                                <div class="col-span-2">
                                                    <select id="product-select" @change="updateSelectedProduct()" class="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 shadow-sm sm:text-sm border-gray-300">
                                                        <option value="">Select a product</option>
                                                        <?php foreach ($products as $product): ?>
                                                            <option value="<?= $product['id'] ?>" data-name="<?= htmlspecialchars($product['product']) ?>" data-price="<?= $product['price'] ?>">
                                                                <?= htmlspecialchars($product['product']) ?> - $<?= number_format($product['price'], 2) ?> (Stock: <?= $product['stock'] ?>)
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                                <div>
                                                    <input type="number" x-model="quantity" placeholder="Quantity" min="1" class="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 shadow-sm sm:text-sm border-gray-300">
                                                </div>
                                                <div>
                                                    <button type="button"
                                                            @click="addProductToOrder(selectedProductId, selectedProductName, selectedProductPrice, quantity); quantity = 1;"
                                                            :disabled="!selectedProductId"
                                                            :class="{'bg-indigo-600 hover:bg-indigo-700': selectedProductId, 'bg-gray-400 cursor-not-allowed': !selectedProductId}"
                                                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:text-sm">
                                                        Add to Order
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Order Items Table -->
                                            <div class="overflow-x-auto">
                                                <table class="min-w-full divide-y divide-gray-200">
                                                    <thead class="bg-gray-50">
                                                        <tr>
                                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subtotal</th>
                                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody class="bg-white divide-y divide-gray-200">
                                                        <template x-for="(item, index) in orderItems" :key="index">
                                                            <tr>
                                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="item.productName"></td>
                                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$<span x-text="item.price"></span></td>
                                                                <td class="px-6 py-4 whitespace-nowrap">
                                                                    <input type="number" x-model="item.quantity" min="1" class="w-16 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md px-2 py-1">
                                                                </td>
                                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$<span x-text="(item.price * item.quantity).toFixed(2)"></span></td>
                                                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                                    <button @click="removeOrderItem(index)" type="button" class="text-red-600 hover:text-red-900">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        </template>
                                                        <!-- Empty state -->
                                                        <tr x-show="orderItems.length === 0">
                                                            <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">
                                                                No products added to this order yet
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                    <tfoot>
                                                        <tr>
                                                            <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900">Total:</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">$<span x-text="calculateTotal()"></span></td>
                                                            <td></td>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Payment Information -->
                                    <div class="sm:col-span-3">
                                        <label for="payment-method" class="block text-sm font-medium text-gray-700">Payment Method</label>
                                        <select id="payment-method" name="payment-method" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                            <option>Credit Card</option>
                                            <option>Cash</option>
                                            <option>Bank Transfer</option>
                                            <option>PayPal</option>
                                        </select>
                                    </div>

                                    <!-- Order Status -->
                                    <div class="sm:col-span-3">
                                        <label for="order-status" class="block text-sm font-medium text-gray-700">Order Status</label>
                                        <select id="order-status" name="order-status" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                            <option>Pending</option>
                                            <option>Processing</option>
                                            <option>Shipped</option>
                                            <option>Delivered</option>
                                        </select>
                                    </div>

                                    <!-- Notes -->
                                    <div class="sm:col-span-6">
                                        <label for="notes" class="block text-sm font-medium text-gray-700">Order Notes</label>
                                        <div class="mt-1">
                                            <textarea id="notes" name="notes" rows="3" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md p-2"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" @click="submitOrder()"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Create Order
                    </button>
                    <button @click="showCreateOrderModal = false" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Order Details Modal -->
    <div id="viewOrderModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Order Details</h3>
                    <button onclick="closeViewOrderModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div id="orderDetailsContent" class="space-y-4">
                    <!-- Order details will be loaded here -->
                </div>

                <div class="flex justify-end pt-4">
                    <button onclick="closeViewOrderModal()"
                            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Order Status Modal -->
    <div id="editStatusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Update Order Status</h3>
                    <button onclick="closeEditStatusModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form method="POST">
                    <input type="hidden" name="action" value="update_order_status">
                    <input type="hidden" name="order_id" id="editOrderId">

                    <div class="mb-4">
                        <label for="newStatus" class="block text-sm font-medium text-gray-700 mb-2">New Status</label>
                        <select name="new_status" id="newStatus" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option value="pending">Pending</option>
                            <option value="processing">Processing</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="refunded">Refunded</option>
                        </select>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeEditStatusModal()"
                                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Update Status
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Order Confirmation Modal -->
    <div id="deleteOrderModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mt-2">Delete Order</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500">
                        Are you sure you want to delete order <span id="deleteOrderNumber" class="font-medium"></span>?
                        This action cannot be undone and will also delete all associated order items.
                    </p>
                </div>
                <div class="flex justify-center space-x-3 px-4 py-3">
                    <button onclick="closeDeleteOrderModal()"
                            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Cancel
                    </button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete_order">
                        <input type="hidden" name="order_id" id="deleteOrderIdInput">
                        <button type="submit"
                                class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            Delete Order
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Alpine.js data extension for order management
        document.addEventListener('alpine:init', () => {
            Alpine.data('orderManager', () => ({
                // Add submitOrder method to the existing Alpine data
                submitOrder() {
                    if (this.orderItems.length === 0) {
                        alert('Please add at least one product to the order.');
                        return;
                    }

                    if (!this.selectedCustomer) {
                        alert('Please select a customer.');
                        return;
                    }

                    // Prepare order data
                    const orderData = {
                        action: 'create_order',
                        customer_id: this.selectedCustomer,
                        order_date: document.getElementById('order-date').value || new Date().toISOString().split('T')[0],
                        payment_method: document.getElementById('payment-method').value,
                        status: document.getElementById('order-status').value,
                        notes: document.getElementById('notes').value,
                        order_items: JSON.stringify(this.orderItems.map(item => ({
                            product_id: item.productId,
                            quantity: item.quantity,
                            price: item.price
                        })))
                    };

                    // Create form and submit
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.style.display = 'none';

                    Object.keys(orderData).forEach(key => {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = key;
                        input.value = orderData[key];
                        form.appendChild(input);
                    });

                    document.body.appendChild(form);
                    form.submit();
                }
            }))
        });

        // Modal functions
        function openViewOrderModal() {
            document.getElementById('viewOrderModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeViewOrderModal() {
            document.getElementById('viewOrderModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function openEditStatusModal() {
            document.getElementById('editStatusModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeEditStatusModal() {
            document.getElementById('editStatusModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function openDeleteOrderModal() {
            document.getElementById('deleteOrderModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeDeleteOrderModal() {
            document.getElementById('deleteOrderModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Order management functions
        function viewOrderDetails(orderId) {
            // Fetch order details via AJAX
            fetch('get_order_details.php?id=' + orderId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayOrderDetails(data.order);
                        openViewOrderModal();
                    } else {
                        alert('Error loading order details: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading order details. Please try again.');
                });
        }

        function displayOrderDetails(order) {
            const statusClasses = {
                'pending': 'bg-yellow-100 text-yellow-800',
                'processing': 'bg-blue-100 text-blue-800',
                'completed': 'bg-green-100 text-green-800',
                'cancelled': 'bg-red-100 text-red-800',
                'refunded': 'bg-gray-100 text-gray-800'
            };
            const statusClass = statusClasses[order.status] || 'bg-gray-100 text-gray-800';

            let itemsHtml = '';
            if (order.items && order.items.length > 0) {
                itemsHtml = order.items.map(item => `
                    <tr>
                        <td class="px-4 py-2 text-sm text-gray-900">${item.product_name}</td>
                        <td class="px-4 py-2 text-sm text-gray-900">$${parseFloat(item.price).toFixed(2)}</td>
                        <td class="px-4 py-2 text-sm text-gray-900">${item.quantity}</td>
                        <td class="px-4 py-2 text-sm text-gray-900">$${parseFloat(item.subtotal).toFixed(2)}</td>
                    </tr>
                `).join('');
            } else {
                itemsHtml = '<tr><td colspan="4" class="px-4 py-2 text-center text-gray-500">No items found</td></tr>';
            }

            document.getElementById('orderDetailsContent').innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Order Number</label>
                            <p class="text-lg font-semibold text-gray-900">${order.order_number}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Customer</label>
                            <p class="text-sm text-gray-900">${order.customer_name || 'N/A'}</p>
                            <p class="text-xs text-gray-500">${order.customer_email || ''}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Order Date</label>
                            <p class="text-sm text-gray-900">${new Date(order.order_date).toLocaleDateString()}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
                                ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                            </span>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Total Amount</label>
                            <p class="text-lg font-semibold text-gray-900">$${parseFloat(order.total_amount).toFixed(2)}</p>
                        </div>
                        ${order.notes ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Notes</label>
                            <p class="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">${order.notes}</p>
                        </div>
                        ` : ''}
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Created</label>
                            <p class="text-xs text-gray-500">${new Date(order.created_at).toLocaleString()}</p>
                        </div>
                    </div>
                </div>
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Order Items</label>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Product</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Price</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Quantity</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Subtotal</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                ${itemsHtml}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        function editOrderStatus(orderId, currentStatus) {
            document.getElementById('editOrderId').value = orderId;
            document.getElementById('newStatus').value = currentStatus;
            openEditStatusModal();
        }

        function deleteOrder(orderId, orderNumber) {
            document.getElementById('deleteOrderNumber').textContent = orderNumber;
            document.getElementById('deleteOrderIdInput').value = orderId;
            openDeleteOrderModal();
        }

        // Close modals when clicking outside
        document.addEventListener('click', function(e) {
            if (e.target.id === 'viewOrderModal') closeViewOrderModal();
            if (e.target.id === 'editStatusModal') closeEditStatusModal();
            if (e.target.id === 'deleteOrderModal') closeDeleteOrderModal();
        });

        // Close modals on Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeViewOrderModal();
                closeEditStatusModal();
                closeDeleteOrderModal();
            }
        });

        // Auto-submit search form on Enter key
        document.querySelector('input[name="search"]').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                this.form.submit();
            }
        });
    </script>
</body>
</html>
