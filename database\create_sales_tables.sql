-- Create sales and related tables for meat management system
-- Run this SQL in your MySQL database to create the required tables

-- Create sales table
CREATE TABLE IF NOT EXISTS `sales` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `sale_date` date NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method` enum('Cash','Credit Card','Debit Card','Bank Transfer','Check','Mobile Payment') NOT NULL DEFAULT 'Cash',
  `payment_status` enum('Paid','Pending','Partial','Refunded') NOT NULL DEFAULT 'Paid',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON><PERSON>AR<PERSON> (`id`),
  <PERSON>EY `idx_customer_id` (`customer_id`),
  KEY `idx_sale_date` (`sale_date`),
  <PERSON>EY `idx_created_by` (`created_by`),
  KEY `idx_payment_status` (`payment_status`),
  FOREIGN KEY (`customer_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create sale_items table
CREATE TABLE IF NOT EXISTS `sale_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sale_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_sale_id` (`sale_id`),
  KEY `idx_product_id` (`product_id`),
  FOREIGN KEY (`sale_id`) REFERENCES `sales`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create indexes for better performance
CREATE INDEX `idx_sales_date_range` ON `sales` (`sale_date`, `total_amount`);
CREATE INDEX `idx_sale_items_summary` ON `sale_items` (`sale_id`, `quantity`, `subtotal`);

-- Sample data for testing (optional)
-- Note: Make sure you have customers and products in your database first

-- Sample sales (uncomment if you want test data)
/*
INSERT INTO `sales` (`customer_id`, `sale_date`, `total_amount`, `payment_method`, `payment_status`, `notes`, `created_by`, `created_at`, `updated_at`) VALUES
(1, '2024-01-15', 45.98, 'Cash', 'Paid', 'Regular customer purchase', 1, NOW(), NOW()),
(2, '2024-01-15', 78.50, 'Credit Card', 'Paid', 'Bulk order for restaurant', 1, NOW(), NOW()),
(3, '2024-01-16', 32.25, 'Cash', 'Paid', 'Small family order', 1, NOW(), NOW()),
(1, '2024-01-16', 156.75, 'Bank Transfer', 'Paid', 'Large order for event', 1, NOW(), NOW()),
(2, '2024-01-17', 89.99, 'Credit Card', 'Paid', 'Weekly restaurant supply', 1, NOW(), NOW());
*/

-- Sample sale items (uncomment if you want test data and have the sales above)
/*
INSERT INTO `sale_items` (`sale_id`, `product_id`, `quantity`, `unit_price`, `subtotal`, `created_at`) VALUES
(1, 1, 2, 12.99, 25.98, NOW()),
(1, 2, 1, 19.99, 19.99, NOW()),
(2, 3, 3, 15.50, 46.50, NOW()),
(2, 1, 1, 12.99, 12.99, NOW()),
(2, 4, 2, 9.50, 19.00, NOW()),
(3, 2, 1, 19.99, 19.99, NOW()),
(3, 5, 1, 12.25, 12.25, NOW()),
(4, 1, 5, 12.99, 64.95, NOW()),
(4, 3, 6, 15.50, 93.00, NOW()),
(5, 2, 2, 19.99, 39.98, NOW()),
(5, 4, 3, 9.50, 28.50, NOW()),
(5, 6, 1, 21.50, 21.50, NOW());
*/

-- Create a view for sales summary
CREATE OR REPLACE VIEW `sales_summary` AS
SELECT 
    s.id,
    s.sale_date,
    u.name as customer_name,
    u.email as customer_email,
    s.total_amount,
    s.payment_method,
    s.payment_status,
    s.notes,
    creator.name as created_by_name,
    s.created_at,
    COUNT(si.id) as total_items,
    SUM(si.quantity) as total_quantity
FROM sales s
LEFT JOIN users u ON s.customer_id = u.id
LEFT JOIN users creator ON s.created_by = creator.id
LEFT JOIN sale_items si ON s.id = si.sale_id
GROUP BY s.id, s.sale_date, u.name, u.email, s.total_amount, s.payment_method, s.payment_status, s.notes, creator.name, s.created_at
ORDER BY s.created_at DESC;

-- Create a view for detailed sales with items
CREATE OR REPLACE VIEW `sales_with_items` AS
SELECT 
    s.id as sale_id,
    s.sale_date,
    u.name as customer_name,
    s.total_amount,
    s.payment_method,
    s.payment_status,
    si.id as item_id,
    p.product as product_name,
    p.category as product_category,
    si.quantity,
    si.unit_price,
    si.subtotal,
    s.created_at
FROM sales s
LEFT JOIN users u ON s.customer_id = u.id
LEFT JOIN sale_items si ON s.id = si.sale_id
LEFT JOIN products p ON si.product_id = p.id
ORDER BY s.created_at DESC, si.id;
